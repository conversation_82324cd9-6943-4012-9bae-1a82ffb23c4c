from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from models import User, Board, Task, Column, Cell
from forms import LoginForm, RegisterForm, BoardForm, ColumnForm, TaskForm, get_users
from extensions import db
from datetime import datetime
from wtforms import StringField, SelectField, DateField

from wtforms.validators import InputRequired
from flask_wtf import FlaskForm

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('main.login'))

@main_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            return redirect(url_for('main.dashboard'))
        flash("Nom d'utilisateur ou mot de passe incorrect.")
    return render_template('login.html', form=form)

@main_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    form = RegisterForm()
    if form.validate_on_submit():
        existing_user = User.query.filter_by(username=form.username.data).first()
        existing_email = User.query.filter_by(email=form.email.data).first()
        if existing_user:
            flash("Nom d'utilisateur déjà utilisé.")
            return redirect(url_for('main.register'))
        if existing_email:
            flash("Adresse e-mail déjà utilisée.")
            return redirect(url_for('main.register'))
        user = User(username=form.username.data, email=form.email.data)
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        flash("Compte créé avec succès, veuillez vous connecter.")
        return redirect(url_for('main.login'))
    return render_template('register.html', form=form)

@main_bp.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('main.login'))

@main_bp.route('/dashboard')
@login_required
def dashboard():
    boards = Board.query.filter_by(owner_id=current_user.id).all()
    return render_template('dashboard.html', boards=boards)

@main_bp.route('/create_board', methods=['GET', 'POST'])
@login_required
def create_board():
    form = BoardForm()
    if form.validate_on_submit():
        board = Board(title=form.title.data, description=form.description.data, owner_id=current_user.id)
        db.session.add(board)
        db.session.commit()
        flash("Tableau créé avec succès.")
        return redirect(url_for('main.dashboard'))
    return render_template('create_board.html', form=form)

@main_bp.route('/board/<int:board_id>', methods=['GET', 'POST'])
@login_required
def board_detail(board_id):
    board = Board.query.get_or_404(board_id)
    tasks = Task.query.filter_by(board_id=board_id).all()
    columns = Column.query.filter_by(board_id=board.id).all()

    class DynamicTaskForm(FlaskForm):
        title = StringField("Titre", validators=[InputRequired(message="Ce champ est requis.")])
        description = StringField("Description")
        status = SelectField("Statut", choices=[
            ("En cours", "En cours"),
            ("Terminé", "Terminé"),
            ("En attente", "En attente")
        ])
        assigned_to = SelectField("Assigné à", coerce=int, choices=[(0, '-- Choisir un utilisateur --')] + [(u.id, u.username) for u in User.query.all()])
        submit = SubmitField("Créer tâche")

    for col in columns:
        if col.type == "text":
            setattr(DynamicTaskForm, f"col_{col.id}", StringField(col.name))
        elif col.type == "date":
            setattr(DynamicTaskForm, f"col_{col.id}", DateField(col.name, format='%Y-%m-%d'))
        elif col.type == "status":
            setattr(DynamicTaskForm, f"col_{col.id}", SelectField(col.name, choices=[
                ("En attente", "En attente"),
                ("En cours", "En cours"),
                ("Terminé", "Terminé")
            ]))
        elif col.type == "user":
            setattr(DynamicTaskForm, f"col_{col.id}", SelectField(col.name, coerce=int, choices=[(0, '-- Choisir un utilisateur --')] + [(u.id, u.username) for u in User.query.all()]))

    form = DynamicTaskForm()

    if form.validate_on_submit():
        assigned_user_id = form.assigned_to.data if form.assigned_to.data != 0 else None
        new_task = Task(
            title=form.title.data,
            description=form.description.data,
            status=form.status.data,
            assigned_to_id=assigned_user_id,
            board_id=board.id
        )
        db.session.add(new_task)
        db.session.commit()

        for col in columns:
            field_name = f"col_{col.id}"
            value = form[field_name].data
            if col.type == "date" and isinstance(value, datetime):
                value = value.strftime('%Y-%m-%d')
            elif col.type == "user" and value:
                value = str(value.id)
            cell = Cell(task_id=new_task.id, column_id=col.id, value=str(value) if value else '')
            db.session.add(cell)

        db.session.commit()
        flash("Tâche et données personnalisées ajoutées.")
        return redirect(url_for('main.board_detail', board_id=board.id))

    # Ajouter users au template pour afficher les noms d'utilisateurs dans le tableau des tâches
    users = get_users()
    return render_template('board_detail.html', board=board, tasks=tasks, form=form, columns=columns, users=users)

@main_bp.route('/add_column/<int:board_id>', methods=['GET', 'POST'])
@login_required
def add_column(board_id):
    board = Board.query.get_or_404(board_id)
    form = ColumnForm()
    if form.validate_on_submit():
        new_col = Column(name=form.name.data, type=form.type.data, board_id=board.id)
        db.session.add(new_col)
        db.session.commit()
        flash("Colonne ajoutée.")
        return redirect(url_for('main.board_detail', board_id=board.id))
    return render_template('add_column.html', form=form, board=board)

@main_bp.route('/update_cell', methods=['POST'])
@login_required
def update_cell():
    data = request.get_json()
    cell_id = data.get('cell_id')
    new_value = data.get('value')

    cell = Cell.query.get(cell_id)
    if not cell:
        return jsonify({"success": False, "message": "Cellule introuvable"}), 404

    cell.value = new_value
    db.session.commit()

    return jsonify({"success": True, "value": new_value})

@main_bp.route('/task/<int:task_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_task(task_id):
    task = Task.query.get_or_404(task_id)
    board = Board.query.get_or_404(task.board_id)
    columns = Column.query.filter_by(board_id=board.id).all()

    class DynamicTaskForm(FlaskForm):
        title = StringField("Titre", validators=[InputRequired(message="Ce champ est requis.")])
        description = StringField("Description")
        status = SelectField("Statut", choices=[
            ("En cours", "En cours"),
            ("Terminé", "Terminé"),
            ("En attente", "En attente")
        ])
        assigned_to = SelectField("Assigné à", coerce=int, choices=[(0, '-- Choisir un utilisateur --')] + [(u.id, u.username) for u in User.query.all()])
        submit = SubmitField("Modifier tâche")

    for col in columns:
        if col.type == "text":
            setattr(DynamicTaskForm, f"col_{col.id}", StringField(col.name))
        elif col.type == "date":
            setattr(DynamicTaskForm, f"col_{col.id}", DateField(col.name, format='%Y-%m-%d'))
        elif col.type == "status":
            setattr(DynamicTaskForm, f"col_{col.id}", SelectField(col.name, choices=[
                ("En attente", "En attente"),
                ("En cours", "En cours"),
                ("Terminé", "Terminé")
            ]))
        elif col.type == "user":
            setattr(DynamicTaskForm, f"col_{col.id}", SelectField(col.name, coerce=int, choices=[(0, '-- Choisir un utilisateur --')] + [(u.id, u.username) for u in User.query.all()]))

    form = DynamicTaskForm(obj=task)
    form.assigned_to.data = task.assigned_to_id if task.assigned_to_id else 0

    for col in columns:
        field_name = f"col_{col.id}"
        cell = next((c for c in task.cells if c.column_id == col.id), None)
        if cell:
            if col.type == "date":
                try:
                    form[field_name].data = datetime.strptime(cell.value, '%Y-%m-%d').date()
                except Exception:
                    form[field_name].data = None
            elif col.type == "user" and cell.value:
                form[field_name].data = int(cell.value)
            else:
                form[field_name].data = cell.value

    if form.validate_on_submit():
        task.title = form.title.data
        task.description = form.description.data
        task.status = form.status.data
        task.assigned_to_id = form.assigned_to.data if form.assigned_to.data != 0 else None

        for col in columns:
            field_name = f"col_{col.id}"
            value = form[field_name].data
            if col.type == "date" and value:
                value = value.strftime('%Y-%m-%d')
            elif col.type == "user" and value:
                value = str(value.id)

            cell = next((c for c in task.cells if c.column_id == col.id), None)
            if cell:
                cell.value = str(value) if value else ''
            else:
                new_cell = Cell(task_id=task.id, column_id=col.id, value=str(value) if value else '')
                db.session.add(new_cell)

        db.session.commit()
        flash("Tâche modifiée avec succès.")
        return redirect(url_for('main.board_detail', board_id=board.id))

    return render_template('edit_task.html', form=form, board=board, task=task)

@main_bp.route('/task/<int:task_id>/delete', methods=['POST'])
@login_required
def delete_task(task_id):
    task = Task.query.get_or_404(task_id)
    board_id = task.board_id
    db.session.delete(task)
    db.session.commit()
    flash("Tâche supprimée.")
    return redirect(url_for('main.board_detail', board_id=board_id))
# routes.py - Routes principales de l'application
