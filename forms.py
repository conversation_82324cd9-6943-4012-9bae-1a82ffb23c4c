from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField, SelectField, DateField, TextAreaField
from wtforms.validators import InputRequired, Length, Email, EqualTo
from models import User

def get_users():
    return User.query.all()

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>("Nom d'utilisateur", validators=[InputRequired(message="Ce champ est requis.")])
    password = PasswordField("Mot de passe", validators=[InputRequired(message="Ce champ est requis.")])
    submit = SubmitField("Se connecter")

class RegisterForm(FlaskForm):
    username = StringField("Nom d'utilisateur", validators=[
        InputRequired(message="Ce champ est requis."),
        Length(min=4, max=20, message="Le nom d'utilisateur doit contenir entre 4 et 20 caractères.")
    ])
    email = StringField("Adresse e-mail", validators=[
        InputRequired(message="Ce champ est requis."),
        <PERSON><PERSON>(message="Veuillez entrer une adresse e-mail valide.")
    ])
    password = PasswordField("Mot de passe", validators=[
        InputRequired(message="Ce champ est requis."),
        Length(min=6, message="Le mot de passe doit contenir au moins 6 caractères.")
    ])
    password2 = PasswordField("Confirmer le mot de passe", validators=[
        InputRequired(message="Ce champ est requis."),
        EqualTo('password', message="Les mots de passe ne correspondent pas.")
    ])
    submit = SubmitField("Créer un compte")

class BoardForm(FlaskForm):
    title = StringField("Titre du tableau", validators=[
        InputRequired(message="Ce champ est requis."),
        Length(min=1, max=100, message="Le titre doit contenir entre 1 et 100 caractères.")
    ])
    description = TextAreaField("Description")
    submit = SubmitField("Créer le tableau")

class TaskForm(FlaskForm):
    title = StringField("Titre de la tâche", validators=[
        InputRequired(message="Ce champ est requis."),
        Length(min=1, max=100, message="Le titre doit contenir entre 1 et 100 caractères.")
    ])
    description = TextAreaField("Description")
    status = SelectField("Statut", choices=[
        ('En attente', 'En attente'),
        ('En cours', 'En cours'),
        ('Terminé', 'Terminé')
    ])
    assigned_to = SelectField("Assigné à", coerce=int)
    submit = SubmitField("Ajouter la tâche")

class ColumnForm(FlaskForm):
    name = StringField("Nom de la colonne", validators=[
        InputRequired(message="Ce champ est requis."),
        Length(min=1, max=100, message="Le nom doit contenir entre 1 et 100 caractères.")
    ])
    type = SelectField("Type de colonne", choices=[
        ("text", "Texte"),
        ("number", "Nombre"),
        ("date", "Date"),
        ("status", "Statut"),
        ("user", "Utilisateur")
    ])
    submit = SubmitField("Ajouter la colonne")
